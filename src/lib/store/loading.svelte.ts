/**
 * Loading state store for manual control of loading indicators
 * This can be used when navigating state doesn't capture all loading scenarios
 */

let isLoading = $state(false);
let loadingMessage = $state('');

export const loading = {
	get isLoading() {
		return isLoading;
	},
	get message() {
		return loadingMessage;
	},
	show(message = '') {
		console.log('Loading store: Showing loading with message:', message);
		loadingMessage = message;
		isLoading = true;
	},
	hide() {
		console.log('Loading store: Hiding loading');
		isLoading = false;
		loadingMessage = '';
	}
};
