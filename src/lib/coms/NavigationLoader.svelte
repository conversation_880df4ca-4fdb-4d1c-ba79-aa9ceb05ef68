
<script lang="ts">
	import { navigating } from '$app/state';
	import { fade } from 'svelte/transition';
	import { browser } from '$app/environment';
	import { loading } from '$lib/store/loading.svelte';

	let show = $state(false);
	let delayTimer: NodeJS.Timeout;
	let isSidebarToggled = $state(false);

	// Combine navigation state and manual loading state
	let shouldShow = $derived(show || loading.isLoading);

	// Track navigation state
	$effect(() => {
		console.log('NavigationLoader: navigating state changed:', navigating);
		if (navigating && ['form', 'goto', 'leave', 'link'].includes(navigating.type ?? '')) {
			console.log('NavigationLoader: Starting navigation of type:', navigating.type);
			// Reduce delay for better responsiveness
			delayTimer = setTimeout(() => {
				console.log('NavigationLoader: Showing spinner');
				show = true;
			}, 100);
		} else {
			console.log('NavigationLoader: Clearing navigation, navigating:', navigating);
			clearTimeout(delayTimer);
			show = false;
		}

		return () => clearTimeout(delayTimer);
	});

	// Also listen for form submissions that might not trigger navigating state
	$effect(() => {
		if (!browser) return;

		const handleFormSubmit = () => {
			console.log('NavigationLoader: Form submission detected');
			clearTimeout(delayTimer);
			delayTimer = setTimeout(() => {
				console.log('NavigationLoader: Showing spinner for form submission');
				show = true;
			}, 50);
		};

		const handleFormResponse = () => {
			console.log('NavigationLoader: Form response detected');
			clearTimeout(delayTimer);
			show = false;
		};

		// Listen for form submissions
		document.addEventListener('submit', handleFormSubmit);

		// Listen for page updates (which happen after form responses)
		const observer = new MutationObserver(() => {
			if (show) {
				setTimeout(handleFormResponse, 100);
			}
		});

		observer.observe(document.body, {
			childList: true,
			subtree: true
		});

		return () => {
			document.removeEventListener('submit', handleFormSubmit);
			observer.disconnect();
		};
	});

	// Track sidebar toggle state
	$effect(() => {
		if (!browser) return;

		const checkSidebarState = () => {
			isSidebarToggled = document.body.classList.contains('sb-sidenav-toggled');
		};

		// Check initial state
		checkSidebarState();

		// Create observer to watch for class changes on body
		const observer = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
					checkSidebarState();
				}
			});
		});

		observer.observe(document.body, {
			attributes: true,
			attributeFilter: ['class']
		});

		return () => observer.disconnect();
	});

	// Calculate overlay and spinner positioning based on sidebar state and screen size
	let overlayStyle = $derived(() => {
		if (!browser) return "top: 56px; left: 225px; right: 0; bottom: 0; z-index: 1050; opacity: 0.1;";
		
		const isDesktop = window.innerWidth >= 992;
		
		if (isDesktop && isSidebarToggled) {
			// Sidebar is hidden on desktop - make fullscreen
			return "top: 56px; left: 0; right: 0; bottom: 0; z-index: 1050; opacity: 0.1;";
		} else if (isDesktop) {
			// Sidebar is visible on desktop - exclude sidebar area
			return "top: 56px; left: 225px; right: 0; bottom: 0; z-index: 1050; opacity: 0.1;";
		} else {
			// Mobile - always fullscreen (sidebar overlays)
			return "top: 56px; left: 0; right: 0; bottom: 0; z-index: 1050; opacity: 0.1;";
		}
	});

	let spinnerStyle = $derived(() => {
		if (!browser) return "z-index: 1055; margin-left: 112px;";

		const isDesktop = window.innerWidth >= 992;

		if (isDesktop && isSidebarToggled) {
			// Sidebar is hidden on desktop - center in full screen
			return "z-index: 1055; margin-left: 0;";
		} else if (isDesktop) {
			// Sidebar is visible on desktop - center in content area (offset by half sidebar width)
			return "z-index: 1055; margin-left: 112px;";
		} else {
			// Mobile - center in full screen
			return "z-index: 1055; margin-left: 0;";
		}
	});

	$inspect(navigating)
</script>

{#if shouldShow}
	<!-- Dark overlay -->
	<div
		class="position-fixed bg-dark rounded m-1"
		style={overlayStyle()}
		in:fade={{ duration: 200 }}
		out:fade={{ duration: 100 }}
	></div>

	<!-- Spinner -->
	<div
		class="position-fixed top-50 start-50 translate-middle"
		style={spinnerStyle()}
		in:fade={{ duration: 200 }}
		out:fade={{ duration: 100 }}
	>
		<div class="bg-white rounded-circle p-3 shadow-lg border">
			<i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
		</div>
	</div>
{/if}
