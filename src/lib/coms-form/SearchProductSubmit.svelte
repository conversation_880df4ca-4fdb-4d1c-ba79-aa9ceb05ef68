<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { locale } from '$lib/translations/locales.svelte';
	import type { EventHandler } from 'svelte/elements';
	import Form from './Form.svelte';
	import { loading } from '$lib/store/loading.svelte';
	interface Props {
		items: { name: any; id: any; price: any }[];
		height?: string;
		placeholder?: string;
		children?: import('svelte').Snippet;
		action: string;
		q_name?: string;
		billing_id?: number;
	}

	let {
		items = $bindable(),
		height = '300',
		placeholder = locale.T('search'),
		children,
		action,
		q_name,
		billing_id
	}: Props = $props();
	let q = $state(page.url.searchParams.get(q_name ?? '') || '');
	let timeout: number | NodeJS.Timeout | null = $state(null);
	const handleQ: EventHandler<Event, HTMLInputElement> = ({ currentTarget }) => {
		clearTimeout(timeout!);
		const value = currentTarget?.value;
		if (!value) q = '';
		timeout = setTimeout(() => {
			if (q_name) {
				const newUrl = new URL(page.url);
				newUrl?.searchParams?.set(q_name, currentTarget?.value);
				goto(newUrl, { keepFocus: true, noScroll: true });
			} else {
				q = value;
			}
		}, 400);
	};
	async function pushParam() {
		try {
			loading.show('Adding product...');
			const formData = new FormData();
			formData.append('product_id', items[0].id);
			formData.append('price', items[0].price);
			if (billing_id) {
				formData.append('billing_id', billing_id.toString());
			}
			await fetch(action, {
				method: 'POST',
				body: formData
			});
			const newUrl = new URL(page.url);
			if (q_name) {
				newUrl?.searchParams?.set(q_name, '');
				goto(newUrl, { keepFocus: true, noScroll: true });
			}
			q = '';
		} catch (error) {
			console.error('Error adding product:', error);
		} finally {
			loading.hide();
		}
	}
	$effect(() => {
		if (items?.length === 1 && q) {
			pushParam();
		}
	});
</script>

<div class="dropdown form-control m-0 p-0 shadow-none border-0">
	<input
		oninput={handleQ}
		{placeholder}
		name="q"
		autocomplete="off"
		data-bs-toggle="dropdown"
		id="dropdown"
		bind:value={q}
		class="form-control"
		type="text"
	/>
	<ul style="width: 100%;" class="dropdown-menu">
		<div style="max-height: {height.concat('px')}; overflow-y: auto;">
			{#if items?.length}
				{#each items || [] as item (item.id)}
					<Form data_sveltekit_keepfocus method="post" {action}>
						<li>
							{@render children?.()}
							<input type="hidden" name="product_id" value={item.id} />
							<input type="hidden" name="price" value={item.price} />
							<button type="submit" class="dropdown-item">{item.name ?? ''}</button>
						</li>
					</Form>
				{/each}
			{:else}
				<button type="button" class="dropdown-item">
					<i class="fa-solid fa-magnifying-glass"></i>
					{locale.T('none_data')}
				</button>
			{/if}
		</div>
	</ul>
</div>
